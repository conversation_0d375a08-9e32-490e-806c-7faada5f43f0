// Enhanced function to detect and filter thinking content using AI SDK
export function filterThinkingContent(content: string): string {
  try {
    // First, try to use AI SDK's extractReasoningMiddleware approach
    const reasoningResult = extractThinkingTags(content);
    if (reasoningResult.reasoning && reasoningResult.content) {
      console.log('AI SDK: Successfully extracted reasoning content');
      console.log('AI SDK: Reasoning length:', reasoningResult.reasoning.length);
      console.log('AI SDK: Final content length:', reasoningResult.content.length);
      return reasoningResult.content;
    }
  } catch (error) {
    console.warn('AI SDK extraction failed, falling back to regex patterns:', error);
  }

  // Fallback to enhanced regex patterns
  return filterWithRegexPatterns(content);
}

// AI SDK inspired thinking tag extraction
function extractThinkingTags(content: string): { reasoning?: string; content: string } {
  // Enhanced <think> tag patterns inspired by AI SDK
  const thinkTagPatterns = [
    /<think>[\s\S]*?<\/think>/gi,
    /<thinking>[\s\S]*?<\/thinking>/gi,
    /<reason>[\s\S]*?<\/reason>/gi,
    /<analysis>[\s\S]*?<\/analysis>/gi,
    /<internal>[\s\S]*?<\/internal>/gi
  ];

  let reasoning = '';
  let cleanedContent = content;

  // Extract reasoning content from tags
  for (const pattern of thinkTagPatterns) {
    const matches = content.match(pattern);
    if (matches) {
      reasoning += matches.join('\n') + '\n';
      cleanedContent = cleanedContent.replace(pattern, '');
    }
  }

  // Clean up the remaining content
  cleanedContent = cleanedContent.trim();

  // Try to extract structured JSON if present
  const jsonMatch = cleanedContent.match(/\{[\s\S]*\}/);
  if (jsonMatch) {
    cleanedContent = jsonMatch[0];
  }

  return {
    reasoning: reasoning || undefined,
    content: cleanedContent
  };
}

// Enhanced regex-based filtering with better patterns
function filterWithRegexPatterns(content: string): string {
  // Enhanced patterns for thinking models
  const thinkingPatterns = [
    // DeepSeek-R1 style thinking
    /<think>[\s\S]*?<\/think>/gi,
    /<thinking>[\s\S]*?<\/thinking>/gi,
    /<reason>[\s\S]*?<\/reason>/gi,
    /<analysis>[\s\S]*?<\/analysis>/gi,

    // OpenAI o1 style thinking (often starts with specific phrases)
    /^[\s\S]*?(?=\{)/m, // Remove everything before the first JSON object

    // Common thinking indicators
    /^(?:Let me think|I need to|First, let me|Looking at this|I'll analyze)[\s\S]*?(?=\{)/gmi,

    // Reasoning chains that end with "So the answer is" or similar
    /^[\s\S]*?(?:So the answer is|Therefore|In conclusion|The final answer is|Here's the result)[\s\S]*?(?=\{)/gmi,

    // Step-by-step reasoning
    /^(?:Step \d+|First|Second|Third|Finally|Next)[\s\S]*?(?=\{)/gmi,

    // Chain of thought patterns
    /^(?:Thinking step by step|Let's break this down|To solve this)[\s\S]*?(?=\{)/gmi
  ];

  let cleanedContent = content;

  // Apply all thinking patterns
  for (const pattern of thinkingPatterns) {
    cleanedContent = cleanedContent.replace(pattern, '');
  }

  // Enhanced cleanup for common thinking artifacts
  cleanedContent = cleanedContent
    .replace(/^[\s\S]*?(?=\{)/m, '') // Remove everything before first {
    .replace(/\}[\s\S]*$/m, '}') // Remove everything after last }
    .replace(/^[^{]*/, '') // Remove any non-JSON prefix
    .replace(/[^}]*$/, '') // Remove any non-JSON suffix
    .trim();

  return cleanedContent;
}

// Enhanced JSON extraction with proper bracket matching
function extractCompleteJson(content: string): string | null {
  const startIndex = content.indexOf('{');
  if (startIndex === -1) return null;

  let braceCount = 0;
  let inString = false;
  let escaped = false;

  for (let i = startIndex; i < content.length; i++) {
    const char = content[i];

    if (escaped) {
      escaped = false;
      continue;
    }

    if (char === '\\') {
      escaped = true;
      continue;
    }

    if (char === '"') {
      inString = !inString;
      continue;
    }

    if (!inString) {
      if (char === '{') {
        braceCount++;
      } else if (char === '}') {
        braceCount--;
        if (braceCount === 0) {
          return content.substring(startIndex, i + 1);
        }
      }
    }
  }

  // If we reach here, JSON is incomplete - try to complete it
  const incomplete = content.substring(startIndex);
  console.log('Incomplete JSON detected, attempting to complete:', incomplete.substring(0, 200) + '...');
  return incomplete;
}

// Enhanced structured output extraction
export function extractStructuredOutput(content: string): any {
  const attempts = [
    // Attempt 1: Direct parsing
    () => JSON.parse(content),

    // Attempt 2: Extract complete JSON block and parse
    () => {
      const jsonStr = extractCompleteJson(content);
      if (!jsonStr) throw new Error('No JSON block found');
      console.log('Extracted JSON length:', jsonStr.length);
      return JSON.parse(jsonStr);
    },

    // Attempt 3: Fix common issues and parse
    () => {
      const jsonStr = extractCompleteJson(content);
      if (!jsonStr) throw new Error('No JSON block found');
      const fixedJson = fixCommonJsonIssues(jsonStr);
      console.log('Attempting to parse fixed JSON:', fixedJson.substring(0, 200) + '...');
      return JSON.parse(fixedJson);
    },

    // Attempt 4: Aggressive fixing for DeepSeek R1 specific issues
    () => {
      const jsonStr = extractCompleteJson(content);
      if (!jsonStr) throw new Error('No JSON block found');
      let aggressive = jsonStr;

      // Fix specific DeepSeek R1 patterns
      aggressive = aggressive
        // Fix missing commas after quoted values before closing braces
        .replace(/("(?:[^"\\]|\\.)*")\s*}/g, '$1}')
        // Fix missing commas between properties (most common issue)
        .replace(/("(?:[^"\\]|\\.)*")\s*("(?:[^"\\]|\\.)*"\s*:)/g, '$1,$2')
        // Fix missing commas after closing braces/brackets before next property
        .replace(/([}\]])\s*("(?:[^"\\]|\\.)*"\s*:)/g, '$1,$2')
        // Fix specific priority field issue (Chinese characters)
        .replace(/("priority"\s*:\s*"[^"]*")\s*([^,}\]]+)/g, '$1')
        // Fix array formatting
        .replace(/\[\s*"([^"]*)",?\s*"([^"]*)",?\s*"([^"]*)",?\s*"([^"]*)"\s*\]/g, '["$1","$2","$3","$4"]')
        // Remove any trailing content after the last }
        .replace(/}[^}]*$/, '}')
        // Remove trailing commas
        .replace(/,(\s*[}\]])/g, '$1');

      console.log('Attempting aggressive fix:', aggressive);
      return JSON.parse(aggressive);
    },

    // Attempt 5: Character-by-character reconstruction for severely malformed JSON
    () => {
      const jsonStr = extractCompleteJson(content);
      if (!jsonStr) throw new Error('No JSON block found');
      let reconstructed = jsonStr;

      // Very aggressive reconstruction
      reconstructed = reconstructed
        // Ensure proper comma placement after all quoted strings before next property
        .replace(/("(?:[^"\\]|\\.)*")\s+("(?:[^"\\]|\\.)*"\s*:)/g, '$1,$2')
        // Fix nested object comma issues
        .replace(/("(?:[^"\\]|\\.)*")\s*}\s*("(?:[^"\\]|\\.)*"\s*:)/g, '$1},$2')
        // Clean up multiple commas
        .replace(/,+/g, ',')
        // Remove commas before closing brackets
        .replace(/,(\s*[}\]])/g, '$1');

      console.log('Attempting character reconstruction:', reconstructed);
      return JSON.parse(reconstructed);
    },

    // Attempt 6: Bug report specific reconstruction
    () => {
      const jsonStr = extractCompleteJson(content);
      if (!jsonStr) throw new Error('No JSON block found');
      let bugReportFixed = jsonStr;

      // Specific fixes for bug report JSON structure
      bugReportFixed = bugReportFixed
        // Fix description object structure issues
        .replace(/("description"\s*:\s*\{[^}]*)"([^"]*)"([^,}]*)/g, '$1"$2"')
        // Fix priority field specifically
        .replace(/("priority"\s*:\s*"[^"]*")([^,}\]]*)/g, '$1')
        // Fix steps array formatting
        .replace(/("steps_to_reproduce"\s*:\s*\[[^\]]*)\]([^,}\]]*)/g, '$1]')
        // Fix any trailing content after closing braces
        .replace(/}([^,}\]]*$)/g, '}')
        // Ensure commas between top-level properties
        .replace(/}(\s*)("(?:[^"\\]|\\.)*"\s*:)/g, '},$2');

      console.log('Attempting bug report specific fix:', bugReportFixed);
      return JSON.parse(bugReportFixed);
    },

    // Attempt 7: Handle incomplete/truncated JSON
    () => {
      const jsonStr = extractCompleteJson(content);
      if (!jsonStr) throw new Error('No JSON block found');

      // Try to complete incomplete JSON
      let completed = jsonStr;

      // Check if JSON ends abruptly
      const trimmed = completed.trim();
      if (!trimmed.endsWith('}')) {
        console.log('Detected incomplete JSON, attempting to complete...');

        // Count open braces vs close braces
        const openBraces = (completed.match(/\{/g) || []).length;
        const closeBraces = (completed.match(/\}/g) || []).length;
        const missingBraces = openBraces - closeBraces;

        // Add missing closing braces
        if (missingBraces > 0) {
          completed += '}'.repeat(missingBraces);
          console.log(`Added ${missingBraces} missing closing braces`);
        }

        // Fix common incomplete patterns
        completed = completed
          // Fix incomplete string values
          .replace(/"[^"]*$/, '""')
          // Fix incomplete array values
          .replace(/\[[^\]]*$/, '[]')
          // Fix trailing commas before added braces
          .replace(/,(\s*}+)$/, '$1');
      }

      console.log('Attempting to parse completed JSON:', completed.substring(0, 200) + '...');
      return JSON.parse(completed);
    }
  ];

  let lastError;
  for (const attempt of attempts) {
    try {
      return attempt();
    } catch (error) {
      lastError = error;
      console.warn('JSON parsing attempt failed:', error instanceof Error ? error.message : String(error));
    }
  }

  throw new Error(`All JSON parsing attempts failed. Last error: ${lastError instanceof Error ? lastError.message : String(lastError)}`);
}

// Fix common JSON formatting issues
function fixCommonJsonIssues(jsonStr: string): string {
  let fixed = jsonStr.trim();

  // Step 1: Fix missing commas between properties
  // Look for patterns like: "value"},"key" or "value"}"key" and add comma
  fixed = fixed.replace(/("(?:[^"\\]|\\.)*")\s*}\s*,?\s*("(?:[^"\\]|\\.)*"\s*:)/g, '$1},$2');
  fixed = fixed.replace(/("(?:[^"\\]|\\.)*")\s*}\s*("(?:[^"\\]|\\.)*"\s*:)/g, '$1},$2');

  // Step 2: Fix missing commas between object properties
  // Look for patterns like: "value""key": and add comma
  fixed = fixed.replace(/("(?:[^"\\]|\\.)*")\s*("(?:[^"\\]|\\.)*"\s*:)/g, '$1,$2');

  // Step 3: Fix missing commas after closing braces/brackets
  // Look for patterns like: }"key": or ]"key": and add comma
  fixed = fixed.replace(/([}\]])\s*("(?:[^"\\]|\\.)*"\s*:)/g, '$1,$2');

  // Step 4: Remove trailing commas
  fixed = fixed.replace(/,(\s*[}\]])/g, '$1');

  // Step 5: Quote unquoted keys
  fixed = fixed.replace(/([{,]\s*)(\w+):/g, '$1"$2":');

  // Step 6: Replace single quotes with double quotes
  fixed = fixed.replace(/:\s*'([^']*)'/g, ': "$1"');

  // Step 7: Fix escaped characters
  fixed = fixed.replace(/\\n/g, '\\n');

  // Step 8: Fix specific Chinese character issues
  fixed = fixed.replace(/("priority"\s*:\s*"[^"]*")([^,}\]]*)/g, '$1');

  return fixed;
}

// Enhanced reasoning model detection with more patterns
export function isReasoningModel(_provider: string, model: string): boolean {
  const reasoningModels = [
    // DeepSeek reasoning models
    'deepseek-r1',
    'deepseek-reasoner',
    'deepseek-r1-distill',

    // OpenAI reasoning models
    'o1-preview',
    'o1-mini',
    'o1',
    'o1-pro',

    // Claude reasoning models
    'claude-3-reasoning',
    'claude-3.5-reasoning',

    // Other potential reasoning models
    'gpt-4o-reasoning',
    'gemini-reasoning',
    'llama-reasoning',

    // Generic patterns
    /.*-r1$/i,
    /.*-reasoning$/i,
    /.*-think$/i,
    /.*-cot$/i, // Chain of thought
    /o1.*/i,
    /.*reasoning.*/i
  ];

  const modelLower = model.toLowerCase();

  return reasoningModels.some(pattern => {
    if (typeof pattern === 'string') {
      return modelLower.includes(pattern.toLowerCase());
    } else {
      return pattern.test(modelLower);
    }
  });
}

// Middleware-inspired processing function
export function processReasoningResponse(content: string, provider: string, model: string): {
  reasoning?: string;
  content: string;
  structured?: any;
} {
  if (!isReasoningModel(provider, model)) {
    return { content };
  }

  console.log(`Processing reasoning model response: ${provider}/${model}`);

  // Extract thinking content
  const filtered = filterThinkingContent(content);

  // Try to extract structured output
  let structured;
  try {
    structured = extractStructuredOutput(filtered);
    console.log('Successfully extracted structured output');
  } catch (error) {
    console.warn('Failed to extract structured output:', error);
  }

  return {
    reasoning: content !== filtered ? content.replace(filtered, '').trim() : undefined,
    content: filtered,
    structured
  };
}
